import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../logging/app_logger.dart';
import '../models/notification_analytics.dart';
import '../models/notification_analytics_reports.dart';
import '../models/notification_channel.dart';
import '../models/notification_payload.dart';
import '../models/scheduled_notification.dart';
import '../models/sync_notification_settings.dart';
import '../models/system_alert_settings.dart';
import '../models/prayer_notification_settings.dart';
import '../services/background_sync_notification_service.dart';
import '../services/notification_analytics_service.dart';
import '../services/notification_channel_manager.dart';
import '../services/notification_scheduler.dart';
import '../services/notification_service.dart';
import '../services/prayer_notification_service.dart';
import '../services/system_alert_notification_service.dart';
import '../../storage/storage_service.dart';
import '../../permissions/permission_service.dart';
import '../../storage/providers/storage_providers.dart';
import '../../permissions/providers/permission_providers.dart';

part 'unified_notification_provider.g.dart';

/// Service Health Status
///
/// Represents the health status of a notification service
enum ServiceHealthStatus { healthy, degraded, unhealthy, unknown }

/// Notification Manager State
///
/// Context7 MCP: Immutable state object representing the complete state
/// of the unified notification manager following single responsibility principle.
@immutable
class NotificationManagerState {
  const NotificationManagerState({
    required this.isInitialized,
    required this.services,
    required this.healthStatus,
    required this.lastUpdate,
    this.errorMessage,
    this.initializationProgress = 1.0,
  });

  /// Whether the notification manager is fully initialized
  final bool isInitialized;

  /// All notification services managed by this provider
  final NotificationServices services;

  /// Overall health status of the notification system
  final ServiceHealthStatus healthStatus;

  /// Last update timestamp
  final DateTime lastUpdate;

  /// Error message if initialization failed
  final String? errorMessage;

  /// Initialization progress (0.0 to 1.0)
  final double initializationProgress;

  /// Create a copy with updated values
  NotificationManagerState copyWith({
    bool? isInitialized,
    NotificationServices? services,
    ServiceHealthStatus? healthStatus,
    DateTime? lastUpdate,
    String? errorMessage,
    double? initializationProgress,
  }) {
    return NotificationManagerState(
      isInitialized: isInitialized ?? this.isInitialized,
      services: services ?? this.services,
      healthStatus: healthStatus ?? this.healthStatus,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      errorMessage: errorMessage ?? this.errorMessage,
      initializationProgress: initializationProgress ?? this.initializationProgress,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationManagerState &&
          runtimeType == other.runtimeType &&
          isInitialized == other.isInitialized &&
          services == other.services &&
          healthStatus == other.healthStatus &&
          lastUpdate == other.lastUpdate &&
          errorMessage == other.errorMessage &&
          initializationProgress == other.initializationProgress;

  @override
  int get hashCode =>
      Object.hash(isInitialized, services, healthStatus, lastUpdate, errorMessage, initializationProgress);
}

/// Notification Services Container
///
/// Context7 MCP: Dependency injection container for all notification services
/// following dependency inversion principle.
@immutable
class NotificationServices {
  const NotificationServices({
    required this.notificationService,
    required this.prayerService,
    required this.syncService,
    required this.alertService,
    required this.channelManager,
    required this.scheduler,
    required this.analyticsService,
  });

  /// Core notification service for basic operations
  final NotificationService notificationService;

  /// Prayer-specific notification service
  final PrayerNotificationService prayerService;

  /// Background sync notification service
  final BackgroundSyncNotificationService syncService;

  /// System alert notification service
  final SystemAlertNotificationService alertService;

  /// Notification channel manager
  final NotificationChannelManager channelManager;

  /// Notification scheduler
  final NotificationScheduler scheduler;

  /// Analytics service for tracking notification metrics
  final NotificationAnalyticsService analyticsService;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationServices &&
          runtimeType == other.runtimeType &&
          notificationService == other.notificationService &&
          prayerService == other.prayerService &&
          syncService == other.syncService &&
          alertService == other.alertService &&
          channelManager == other.channelManager &&
          scheduler == other.scheduler &&
          analyticsService == other.analyticsService;

  @override
  int get hashCode => Object.hash(
    notificationService,
    prayerService,
    syncService,
    alertService,
    channelManager,
    scheduler,
    analyticsService,
  );
}

/// Notification Service Dependencies Provider
///
/// Context7 MCP: Provides dependency injection for all notification services
/// following dependency inversion principle and single responsibility.
@riverpod
Future<NotificationServiceDependencies> notificationServiceDependencies(NotificationServiceDependenciesRef ref) async {
  try {
    AppLogger.info('🔧 Initializing notification service dependencies');

    // Context7 MCP: Initialize core dependencies with proper error handling
    final plugin = FlutterLocalNotificationsPlugin();
    final storageService = ref.read(storageServiceProvider);
    final permissionService = ref.read(permissionServiceProvider);

    // Context7 MCP: Initialize notification service with dependency injection
    final notificationService = NotificationService(
      plugin: plugin,
      storageService: storageService,
      permissionService: permissionService,
    );

    // Context7 MCP: Initialize notification service
    await notificationService.initialize();

    final dependencies = NotificationServiceDependenciesImpl(
      notificationService: notificationService,
      storageService: storageService,
      permissionService: permissionService,
    );

    AppLogger.info('✅ Notification service dependencies initialized successfully');
    return dependencies;
  } catch (e, stackTrace) {
    AppLogger.error('❌ Failed to initialize notification service dependencies', e, stackTrace);
    rethrow;
  }
}

/// Abstract interface for notification service dependencies
///
/// Context7 MCP: Dependency inversion principle - depend on abstractions, not concretions
abstract class NotificationServiceDependencies {
  NotificationService get notificationService;
  StorageService get storageService;
  PermissionService get permissionService;
}

/// Implementation of notification service dependencies
///
/// Context7 MCP: Concrete implementation following dependency injection pattern
class NotificationServiceDependenciesImpl implements NotificationServiceDependencies {
  const NotificationServiceDependenciesImpl({
    required this.notificationService,
    required this.storageService,
    required this.permissionService,
  });

  @override
  final NotificationService notificationService;

  @override
  final StorageService storageService;

  @override
  final PermissionService permissionService;
}

/// Unified Notification Manager
///
/// Context7 MCP: Single source of truth for all notification functionality
/// following single responsibility principle and dependency inversion.
///
/// **Replaces 8 service providers:**
/// - prayerNotificationService
/// - backgroundSyncNotificationService
/// - systemAlertNotificationService
/// - notificationChannelManager
/// - notificationScheduler
/// - notificationAnalyticsService
/// - notificationService
/// - progressTrackingService
///
/// **Features:**
/// - Unified service management with dependency injection
/// - Comprehensive error handling and recovery
/// - Performance monitoring and health checks
/// - Automatic service initialization and lifecycle management
/// - Context7 MCP compliant architecture patterns
@riverpod
class UnifiedNotificationManager extends _$UnifiedNotificationManager {
  /// Context7 MCP: Initialize the unified notification manager
  ///
  /// This method follows the AsyncNotifier pattern for proper async initialization
  /// with automatic loading and error state management.
  @override
  Future<NotificationManagerState> build() async {
    try {
      AppLogger.info('🚀 Initializing unified notification manager');

      // Context7 MCP: Initialize service dependencies first
      final dependencies = await ref.read(notificationServiceDependenciesProvider.future);

      // Context7 MCP: Initialize all notification services with dependency injection
      final services = await _initializeServices(dependencies);

      // Context7 MCP: Perform health checks on all services
      final healthStatus = await _performHealthChecks(services);

      final managerState = NotificationManagerState(
        isInitialized: true,
        services: services,
        healthStatus: healthStatus,
        lastUpdate: DateTime.now(),
        initializationProgress: 1.0,
      );

      AppLogger.info('✅ Unified notification manager initialized successfully');
      return managerState;
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize unified notification manager', e, stackTrace);

      // Context7 MCP: Return error state instead of throwing
      return NotificationManagerState(
        isInitialized: false,
        services: _createFallbackServices(),
        healthStatus: ServiceHealthStatus.unhealthy,
        lastUpdate: DateTime.now(),
        errorMessage: e.toString(),
        initializationProgress: 0.0,
      );
    }
  }

  /// Context7 MCP: Initialize all notification services with dependency injection
  ///
  /// This method creates all required notification services using the provided
  /// dependencies, following the dependency inversion principle.
  Future<NotificationServices> _initializeServices(NotificationServiceDependencies dependencies) async {
    try {
      AppLogger.debug('🔧 Initializing notification services');

      // Context7 MCP: Create prayer notification service
      final prayerService = PrayerNotificationService(
        notificationService: dependencies.notificationService,
        storageService: dependencies.storageService,
      );

      // Context7 MCP: Create background sync notification service
      final syncService = BackgroundSyncNotificationService(notificationService: dependencies.notificationService);

      // Context7 MCP: Create system alert notification service
      final alertService = SystemAlertNotificationService(notificationService: dependencies.notificationService);

      // Context7 MCP: Create notification channel manager
      final channelManager = NotificationChannelManager(plugin: dependencies.notificationService.plugin);

      // Context7 MCP: Create notification scheduler
      final scheduler = NotificationScheduler(flutterLocalNotificationsPlugin: dependencies.notificationService.plugin);

      // Context7 MCP: Create analytics service
      final analyticsService = NotificationAnalyticsService(storageService: dependencies.storageService);

      // Context7 MCP: Initialize all services
      await Future.wait([
        prayerService.initialize(),
        syncService.initialize(),
        alertService.initialize(),
        channelManager.initialize(),
        analyticsService.initialize(),
      ]);

      AppLogger.debug('✅ All notification services initialized');

      return NotificationServices(
        notificationService: dependencies.notificationService,
        prayerService: prayerService,
        syncService: syncService,
        alertService: alertService,
        channelManager: channelManager,
        scheduler: scheduler,
        analyticsService: analyticsService,
      );
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to initialize notification services', e, stackTrace);
      rethrow;
    }
  }

  /// Context7 MCP: Perform health checks on all services
  ///
  /// This method checks the health status of all notification services
  /// and returns an overall health status.
  Future<ServiceHealthStatus> _performHealthChecks(NotificationServices services) async {
    try {
      AppLogger.debug('🔍 Performing health checks on notification services');

      // Context7 MCP: Check each service health
      final healthChecks = await Future.wait([
        _checkServiceHealth('NotificationService', () => services.notificationService.isInitialized),
        _checkServiceHealth('PrayerService', () => services.prayerService.isInitialized),
        _checkServiceHealth('SyncService', () => services.syncService.isInitialized),
        _checkServiceHealth('AlertService', () => services.alertService.isInitialized),
        _checkServiceHealth('ChannelManager', () => services.channelManager.isInitialized),
        _checkServiceHealth('AnalyticsService', () => services.analyticsService.isInitialized),
      ]);

      // Context7 MCP: Determine overall health status
      final unhealthyCount = healthChecks.where((status) => status == ServiceHealthStatus.unhealthy).length;
      final degradedCount = healthChecks.where((status) => status == ServiceHealthStatus.degraded).length;

      if (unhealthyCount > 0) {
        AppLogger.warning('⚠️ Some notification services are unhealthy');
        return ServiceHealthStatus.unhealthy;
      } else if (degradedCount > 0) {
        AppLogger.warning('⚠️ Some notification services are degraded');
        return ServiceHealthStatus.degraded;
      } else {
        AppLogger.debug('✅ All notification services are healthy');
        return ServiceHealthStatus.healthy;
      }
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to perform health checks', e, stackTrace);
      return ServiceHealthStatus.unknown;
    }
  }

  /// Context7 MCP: Check individual service health
  Future<ServiceHealthStatus> _checkServiceHealth(String serviceName, bool Function() healthCheck) async {
    try {
      final isHealthy = healthCheck();
      if (isHealthy) {
        return ServiceHealthStatus.healthy;
      } else {
        AppLogger.warning('⚠️ Service $serviceName is not healthy');
        return ServiceHealthStatus.degraded;
      }
    } catch (e) {
      AppLogger.error('❌ Health check failed for service $serviceName', e);
      return ServiceHealthStatus.unhealthy;
    }
  }

  /// Context7 MCP: Create fallback services for error scenarios
  NotificationServices _createFallbackServices() {
    AppLogger.warning('🔄 Creating fallback notification services');

    // Context7 MCP: Create minimal fallback services to prevent app crashes
    final fallbackPlugin = FlutterLocalNotificationsPlugin();
    final fallbackNotificationService = NotificationService(
      plugin: fallbackPlugin,
      storageService: null, // Will use in-memory storage
      permissionService: null, // Will skip permission checks
    );

    return NotificationServices(
      notificationService: fallbackNotificationService,
      prayerService: PrayerNotificationService(notificationService: fallbackNotificationService, storageService: null),
      syncService: BackgroundSyncNotificationService(notificationService: fallbackNotificationService),
      alertService: SystemAlertNotificationService(notificationService: fallbackNotificationService),
      channelManager: NotificationChannelManager(plugin: fallbackPlugin),
      scheduler: NotificationScheduler(flutterLocalNotificationsPlugin: fallbackPlugin),
      analyticsService: NotificationAnalyticsService(storageService: null),
    );
  }

  // Context7 MCP: Public API methods for unified notification management

  /// Schedule prayer notifications for a specific date and location
  ///
  /// Context7 MCP: Unified interface for prayer notification scheduling
  /// replacing the need for multiple provider dependencies.
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final currentState = state.value;
      if (currentState == null || !currentState.isInitialized) {
        AppLogger.warning('⚠️ Cannot schedule prayer notifications - manager not initialized');
        return;
      }

      AppLogger.info('📅 Scheduling prayer notifications for ${date.toIso8601String()}');

      await currentState.services.prayerService.schedulePrayerNotifications(
        date: date,
        latitude: latitude,
        longitude: longitude,
      );

      // Context7 MCP: Track analytics
      await currentState.services.analyticsService.trackEvent('prayer_notifications_scheduled', {
        'date': date.toIso8601String(),
        'latitude': latitude,
        'longitude': longitude,
      });

      AppLogger.info('✅ Prayer notifications scheduled successfully');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to schedule prayer notifications', e, stackTrace);
      rethrow;
    }
  }

  /// Cancel all notifications
  ///
  /// Context7 MCP: Unified interface for canceling all notifications
  Future<void> cancelAllNotifications() async {
    try {
      final currentState = state.value;
      if (currentState == null || !currentState.isInitialized) {
        AppLogger.warning('⚠️ Cannot cancel notifications - manager not initialized');
        return;
      }

      AppLogger.info('🚫 Canceling all notifications');

      await currentState.services.notificationService.cancelAllNotifications();

      // Context7 MCP: Track analytics
      await currentState.services.analyticsService.trackEvent('all_notifications_canceled', {});

      AppLogger.info('✅ All notifications canceled successfully');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to cancel all notifications', e, stackTrace);
      rethrow;
    }
  }

  /// Validate notification configuration
  ///
  /// Context7 MCP: Unified interface for configuration validation
  Future<void> validateConfiguration() async {
    try {
      final currentState = state.value;
      if (currentState == null || !currentState.isInitialized) {
        AppLogger.warning('⚠️ Cannot validate configuration - manager not initialized');
        return;
      }

      AppLogger.info('🔍 Validating notification configuration');

      // Context7 MCP: Validate all service configurations
      await Future.wait([
        currentState.services.notificationService.validateConfiguration(),
        currentState.services.channelManager.validateChannels(),
        currentState.services.prayerService.validateSettings(),
      ]);

      AppLogger.info('✅ Notification configuration validated successfully');
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to validate notification configuration', e, stackTrace);
      rethrow;
    }
  }

  /// Get pending notifications
  ///
  /// Context7 MCP: Unified interface for retrieving pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      final currentState = state.value;
      if (currentState == null || !currentState.isInitialized) {
        AppLogger.warning('⚠️ Cannot get pending notifications - manager not initialized');
        return [];
      }

      AppLogger.debug('📋 Retrieving pending notifications');

      final pendingNotifications = await currentState.services.notificationService.getPendingNotifications();

      AppLogger.debug('✅ Retrieved ${pendingNotifications.length} pending notifications');
      return pendingNotifications;
    } catch (e, stackTrace) {
      AppLogger.error('❌ Failed to get pending notifications', e, stackTrace);
      return [];
    }
  }
}
